# Firmware Log Analysis Project - Python Dependencies
# ===================================================
#
# This file contains all Python packages required to run the firmware log
# analysis scripts including:
# - firmware_log_visualizer.py (single file firmware log analysis)
# - grid_comparison_plotter.py (grid layout comparison plots)
# - log_parser_plotter.py (individual parameter plots)
# - quick_start_example.py (demonstration script)
# - test_visualizer.py (testing script)
#
# Installation: pip install -r requirements.txt
#
# Tested with Python 3.11+

# Core Data Processing and Numerical Computing
# ============================================
numpy>=1.24.0
# Used for:
# - Numerical operations and array processing
# - Statistical calculations (mean, std, trend analysis)
# - Polynomial fitting for trend lines (np.polyfit, np.poly1d)
# - Mathematical operations across parameter data
# - Color palette generation and data manipulation

# Data Analysis and Manipulation
# ==============================
pandas>=2.0.0
# Used for:
# - DataFrame creation and manipulation for summary statistics
# - CSV file reading and writing
# - Data aggregation and statistical analysis
# - Parameter data organization and export

# Data Visualization and Plotting
# ===============================
matplotlib>=3.7.0
# Used for:
# - Creating line plots, scatter plots, and grid layouts
# - Subplot management and figure composition
# - High-resolution PNG output (300-500 DPI)
# - Custom styling, colors, markers, and legends
# - Interactive plot features and formatting

# Statistical Data Visualization
# ==============================
seaborn>=0.12.0
# Used for:
# - Enhanced statistical plotting capabilities
# - Color palette management (husl palette)
# - Improved plot styling (seaborn-v0_8 style)
# - Box plots and distribution visualizations

# Python Standard Library Dependencies
# ===================================
# The following packages are part of Python's standard library
# and do not need to be installed separately:
#
# - re (regular expressions for log parsing)
# - os (file system operations and path handling)
# - sys (system-specific parameters and functions)
# - math (mathematical functions for grid calculations)
# - argparse (command-line argument parsing)
# - collections.defaultdict (data structure for parameter storage)
# - pathlib (modern path handling - Path class)
# - typing (type hints - Dict, List, Tuple, Any, Optional)
#
# These are included in all Python 3.7+ installations

# Development and Testing (Optional)
# =================================
# Uncomment the following if you plan to extend or test the codebase:
#
# pytest>=7.0.0
# # For unit testing and test automation
#
# black>=22.0.0
# # For code formatting and style consistency
#
# flake8>=5.0.0
# # For code linting and style checking

# Platform-Specific Notes
# =======================
# 
# macOS:
# - matplotlib may require additional system dependencies
# - If you encounter issues, try: brew install python-tk
#
# Linux:
# - May need: sudo apt-get install python3-tk python3-dev
# - For headless environments: sudo apt-get install python3-matplotlib
#
# Windows:
# - Should work out of the box with pip install
# - Ensure you have Visual C++ redistributables if needed

# Version Compatibility
# =====================
# Minimum Python version: 3.7
# Recommended Python version: 3.9+
# Tested with Python: 3.11
#
# The specified minimum versions ensure:
# - numpy>=1.24.0: Modern array operations and performance improvements
# - pandas>=2.0.0: Enhanced DataFrame operations and CSV handling
# - matplotlib>=3.7.0: Enhanced subplot management and high-DPI output support
# - seaborn>=0.12.0: Modern statistical plotting and styling capabilities
#
# These versions provide all features used in the firmware log analysis scripts
# including advanced plotting capabilities, statistical analysis, data manipulation,
# trend analysis, and grid layouts.

# Script-Specific Dependencies
# ============================
# firmware_log_visualizer.py: pandas, matplotlib, numpy, seaborn
# grid_comparison_plotter.py: matplotlib, numpy
# log_parser_plotter.py: matplotlib, numpy
# quick_start_example.py: matplotlib (imports from firmware_log_visualizer)
# test_visualizer.py: matplotlib (imports from firmware_log_visualizer)
