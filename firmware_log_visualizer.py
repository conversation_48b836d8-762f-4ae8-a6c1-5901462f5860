#!/usr/bin/env python3
"""
Single File Firmware Log Visualizer

This script parses a single firmware log file and generates visualizations
of extracted numerical parameters from Dolby debug logs.

Features:
- Processes a single firmware log file at a time
- Extracts numerical parameters from Dolby debug logs
- Creates multi-subplot figures showing parameter distributions and trends
- Analyzes parameter patterns within the selected file
- Generates clean, readable layouts with proper labels and legends

Author: <PERSON> Li
Date: 07-27-2025
"""

import os
import re
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import seaborn as sns

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class SingleFileFirmwareLogParser:
    """Parser for a single firmware log file containing Dolby debug information."""

    def __init__(self, file_path: Optional[str] = None):
        """
        Initialize the parser.

        Args:
            file_path: Path to the firmware log file to parse
        """
        self.file_path = Path(file_path) if file_path else None
        self.parsed_data = {}
        self.file_info = {}
        
        # Define regex patterns for different parameter types and formats
        self.patterns = {
            # Dolby debug format (parentheses)
            'dBrightness': r'dBrightness\((\d+)\)',
            'dContrast': r'dContrast\((\d+)\)',
            'dSaturation': r'dSaturation\((\d+)\)',
            'dBacklight': r'dBacklight\((\d+)\)',
            'dLocalContrast': r'dLocalContrast\((\d+)\)',
            'dBrightness_PR_on': r'dBrightness_PR_on\((\d+)\)',

            # Gain parameters (parentheses)
            'gainPos_precisionRendering': r'gainPos_precisionRendering\(\s*(\d+)\)',
            'gainPos_dLocalContrast': r'gainPos_dLocalContrast\(\s*(\d+)\)',
            'gainPos_dBrightness': r'gainPos_dBrightness\(\s*(\d+)\)',
            'gainPos_dSaturation': r'gainPos_dSaturation\(\s*(\d+)\)',
            'gainPos_dContrast': r'gainPos_dContrast\(\s*(\d+)\)',
            'gainNeg_dSaturation': r'gainNeg_dSaturation\(\s*(\d+)\)',
            'gainNeg_dContrast': r'gainNeg_dContrast\(\s*(\d+)\)',

            # Dual-ended info parameters (parentheses)
            'confidence': r'confidence\(\s*(\d+)\)',
            'precisionRenderingStrength': r'precisionRenderingStrength\(\s*(\d+)\)',
            'dSaturationPlusOne': r'dSaturationPlusOne\(\s*(\d+)\)',
            'dContrastPlusOne': r'dContrastPlusOne\(\s*(\d+)\)',

            # Level parameters (colon format)
            'mid_boost': r'mid_boost:\s+(\d+)',
            'highlight_stretch': r'highlight_stretch:\s+(\d+)',
            'shadow_drop': r'shadow_drop:\s+(\d+)',
            'contrast_boost': r'contrast_boost:\s+(\d+)',
            'saturation_boost': r'saturation_boost:\s+(\d+)',
            'detail_boost': r'detail_boost:\s+(\d+)',
            'chroma_indicator': r'chroma_indicator:\s+(\d+)',
            'intensity_indicator_PQ': r'intensity_indicator_PQ:\s+(\d+)',
            'chroma_lift': r'chroma_lift:\s+(\d+)',

            # Mapping strength (equals format)
            'UpMappingStrength': r'UpMappingStrength=\s*(\d+)',
        }

        # Additional patterns for structured format (like TC_Flash files)
        self.structured_patterns = {
            # Config values format: "parameter_name: value (scale n)"
            'intensity_level': r'intensity_level:\s+(\d+)',
            'gain_PrecisionRenderingStrength': r'gain_PrecisionRenderingStrength:\s+(\d+)',
            'gain_DLocalContrast': r'gain_DLocalContrast:\s+(\d+)',
            'gain_DBrightness': r'gain_DBrightness:\s+(\d+)',
            'gainPos_DContrast': r'gainPos_DContrast:\s+(\d+)',
            'gainPos_DSaturation': r'gainPos_DSaturation:\s+(\d+)',
            'gainNeg_DContrast': r'gainNeg_DContrast:\s+(\d+)',
            'gainNeg_DSaturation': r'gainNeg_DSaturation:\s+(\d+)',
            'PrecisionRenderingStrength': r'PrecisionRenderingStrength:\s+(\d+)',
            'DBrightness': r'DBrightness:\s+(\d+)',
            'DBrightness_PR_on': r'DBrightness_PR_on:\s+(\d+)',
            'DContrast': r'DContrast:\s+(\d+)',
            'DSaturation': r'DSaturation:\s+(\d+)',
            'DLocalContrast': r'DLocalContrast:\s+(\d+)',

            # DM values format
            'LmOn': r'LmOn\s+(\d+)',
            'LocalMappingStrength': r'LocalMappingStrength\s+(\d+)',
            'dBrightness_structured': r'^\s*dBrightness\s+(\d+)',
            'dBrightnessPRon': r'dBrightnessPRon\s+(\d+)',
            'dContrast_structured': r'^\s*dContrast\s+(\d+)',
            'dSaturation_structured': r'^\s*dSaturation\s+(\d+)',
            'dLocalContrast_structured': r'^\s*dLocalContrast\s+(\d+)',
            'UpMappingStrength_structured': r'^\s*UpMappingStrength\s+(\d+)',
            'TMax': r'TMax\s+(\d+)',
        }
    
    def extract_metadata_from_filename(self, filename: str) -> Dict[str, Any]:
        """
        Extract metadata from filename.

        Args:
            filename: Name of the file

        Returns:
            Dictionary with extracted metadata
        """
        metadata = {'filename': filename}

        # Extract filter value from filename like "L17-IC-filter_0.0416.txt"
        filter_match = re.search(r'filter_([0-9.]+)\.txt', filename)
        if filter_match:
            metadata['filter_value'] = float(filter_match.group(1))
        else:
            metadata['filter_value'] = None

        # Extract other potential identifiers
        # Example: TC_Flash_first_few_frames_pq_log.txt
        if 'TC_' in filename:
            metadata['test_case'] = filename.split('.')[0]

        return metadata
    
    def parse_file(self, file_path: Optional[Path] = None) -> Dict[str, List[float]]:
        """
        Parse the firmware log file.

        Args:
            file_path: Path to the log file (optional, uses self.file_path if not provided)

        Returns:
            Dictionary with parameter names as keys and lists of values
        """
        if file_path is None:
            file_path = self.file_path

        if file_path is None:
            raise ValueError("No file path provided")

        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Initialize data dictionary with all possible parameters
        all_patterns = {**self.patterns, **self.structured_patterns}
        data = {param: [] for param in all_patterns.keys()}

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                # Extract values for each parameter using both pattern sets
                for param_name, pattern in all_patterns.items():
                    if param_name.endswith('_structured'):
                        # Use multiline mode for structured patterns
                        matches = re.findall(pattern, content, re.MULTILINE)
                    else:
                        matches = re.findall(pattern, content)

                    values = [float(match) for match in matches]
                    data[param_name] = values

                # Merge similar parameters (e.g., dBrightness and dBrightness_structured)
                merged_data = {}
                for param_name, values in data.items():
                    if values:  # Only include parameters with data
                        # Remove '_structured' suffix for cleaner naming
                        clean_name = param_name.replace('_structured', '')
                        if clean_name in merged_data:
                            # Combine values if we have both formats
                            merged_data[clean_name].extend(values)
                        else:
                            merged_data[clean_name] = values

                # Also include parameters that don't have duplicates
                for param_name, values in data.items():
                    if values and param_name not in [p.replace('_structured', '') for p in data.keys()]:
                        merged_data[param_name] = values

        except Exception as e:
            print(f"Error parsing file {file_path}: {e}")
            raise

        return merged_data
    
    def parse_single_file(self, file_path: Optional[str] = None) -> None:
        """Parse the specified firmware log file."""
        if file_path:
            self.file_path = Path(file_path)

        if self.file_path is None:
            raise ValueError("No file path specified")

        if not self.file_path.exists():
            raise FileNotFoundError(f"File not found: {self.file_path}")

        print(f"Parsing file: {self.file_path.name}")

        # Extract metadata from filename
        metadata = self.extract_metadata_from_filename(self.file_path.name)

        # Parse the file data
        file_data = self.parse_file()

        # Store parsed data and metadata
        self.parsed_data = file_data
        self.file_info = {
            'file_path': self.file_path,
            'metadata': metadata,
            'data': file_data
        }

        # Count total parameters found
        total_params = sum(len(values) for values in file_data.values() if values)
        params_with_data = sum(1 for values in file_data.values() if values)

        print(f"Parsing completed.")
        print(f"Found {params_with_data} parameters with data")
        print(f"Total parameter measurements: {total_params}")
    
    def get_summary_statistics(self) -> pd.DataFrame:
        """
        Generate summary statistics for the parsed file.

        Returns:
            DataFrame with summary statistics
        """
        if not self.parsed_data:
            raise ValueError("No data available. Please parse a file first.")

        summary_data = []
        filename = self.file_info['file_path'].name
        metadata = self.file_info['metadata']

        for param_name, values in self.parsed_data.items():
            if values:  # Only include parameters with data
                summary_data.append({
                    'filename': filename,
                    'parameter': param_name,
                    'count': len(values),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values),
                    'unique_values': len(set(values)),
                    **metadata  # Include all metadata fields
                })

        return pd.DataFrame(summary_data)


class SingleFileFirmwareLogVisualizer:
    """Visualizer for single firmware log file data."""

    def __init__(self, parser: SingleFileFirmwareLogParser):
        """
        Initialize the visualizer.

        Args:
            parser: Initialized SingleFileFirmwareLogParser instance
        """
        self.parser = parser
        self.colors = plt.cm.Set3(np.linspace(0, 1, 12))  # Fixed color palette
    
    def create_multi_subplot_figure(self, parameters_to_plot: List[str] = None,
                                  figsize: Tuple[int, int] = (20, 15)) -> plt.Figure:
        """
        Create a multi-subplot figure with each parameter on its own subplot.

        Args:
            parameters_to_plot: List of parameter names to plot. If None, plots all.
            figsize: Figure size as (width, height)

        Returns:
            matplotlib Figure object
        """
        if not self.parser.parsed_data:
            raise ValueError("No data available. Please parse a file first.")

        # Get all available parameters with data
        all_params = []
        for param_name, values in self.parser.parsed_data.items():
            if values:  # Only include parameters with data
                all_params.append(param_name)

        if parameters_to_plot is None:
            parameters_to_plot = sorted(all_params)
        else:
            # Filter to only include parameters that have data
            parameters_to_plot = [p for p in parameters_to_plot if p in all_params]

        if not parameters_to_plot:
            raise ValueError("No parameters with data found.")

        # Calculate subplot layout
        n_params = len(parameters_to_plot)
        n_cols = min(4, n_params)  # Maximum 4 columns
        n_rows = (n_params + n_cols - 1) // n_cols  # Ceiling division

        # Create figure and subplots
        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)

        # Create title with file information
        filename = self.parser.file_info['file_path'].name
        fig.suptitle(f'Firmware Log Parameters Analysis - {filename}',
                    fontsize=16, fontweight='bold')

        # Handle case where we have only one subplot
        if n_params == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        elif n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Flatten axes array for easier indexing
        axes_flat = axes.flatten() if n_params > 1 else axes
        
        # Plot each parameter
        for idx, param_name in enumerate(parameters_to_plot):
            ax = axes_flat[idx]

            # Get data for this parameter
            values = self.parser.parsed_data[param_name]

            if values:
                # Create sequence indices for x-axis (showing temporal progression)
                x_indices = np.arange(len(values))

                # Choose visualization based on data characteristics
                unique_values = len(set(values))

                if unique_values == 1:
                    # Constant value - show as horizontal line with scatter
                    ax.axhline(y=values[0], color=self.colors[idx % len(self.colors)],
                              linewidth=2, alpha=0.7, label=f'Constant: {values[0]}')
                    ax.scatter(x_indices[::max(1, len(x_indices)//20)],
                              [values[0]] * len(x_indices[::max(1, len(x_indices)//20)]),
                              color=self.colors[idx % len(self.colors)], s=30, alpha=0.6)

                elif unique_values < 10:
                    # Few unique values - show as step plot with markers
                    ax.step(x_indices, values, where='mid',
                           color=self.colors[idx % len(self.colors)], linewidth=2)
                    ax.scatter(x_indices, values,
                              color=self.colors[idx % len(self.colors)], s=20, alpha=0.7)

                else:
                    # Many unique values - show as line plot
                    ax.plot(x_indices, values,
                           color=self.colors[idx % len(self.colors)], linewidth=1.5, alpha=0.8)

                    # Add trend line if there are enough points
                    if len(values) > 10:
                        z = np.polyfit(x_indices, values, 1)
                        p = np.poly1d(z)
                        ax.plot(x_indices, p(x_indices), "--",
                               color='red', alpha=0.7, linewidth=1, label='Trend')

                # Statistics text box
                stats_text = f'Count: {len(values)}\nMean: {np.mean(values):.2f}\nStd: {np.std(values):.2f}'
                if unique_values > 1:
                    stats_text += f'\nRange: {np.min(values):.1f} - {np.max(values):.1f}'

                ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round',
                       facecolor='wheat', alpha=0.8), fontsize=8)

                ax.set_title(f'{param_name} ({unique_values} unique values)',
                           fontsize=12, fontweight='bold')
                ax.set_xlabel('Measurement Index', fontsize=10)
                ax.set_ylabel('Parameter Value', fontsize=10)
                ax.grid(True, alpha=0.3)

                # Format y-axis to show values clearly
                if np.max(values) > 1000:
                    ax.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))

            else:
                ax.text(0.5, 0.5, f'No data for\n{param_name}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, style='italic')
                ax.set_title(f'{param_name} (No Data)', fontsize=12)
        
        # Hide unused subplots
        for idx in range(n_params, len(axes_flat)):
            axes_flat[idx].set_visible(False)
        
        plt.tight_layout()
        return fig

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Single File Firmware Log Visualizer',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python firmware_log_visualizer.py tests/firmware/L17-IC-filter_0.5.txt
    → Creates: summary.csv, L17-IC-filter_0.5.png

  python firmware_log_visualizer.py tests/idk/TC_Flash_first_few_frames_pq_log.txt
    → Creates: summary.csv, TC_Flash_first_few_frames_pq_log.png

  python firmware_log_visualizer.py --file tests/firmware/L17-IC-filter_1.txt --output ./results
    → Creates: ./results/summary.csv, ./results/L17-IC-filter_1.png
        """
    )

    parser.add_argument('file_path', nargs='?',
                       help='Path to the firmware log file to analyze')
    parser.add_argument('-f', '--file', dest='file_path_alt',
                       help='Alternative way to specify the file path')
    parser.add_argument('-o', '--output', default='.',
                       help='Output directory path (default: current directory)')
    parser.add_argument('--no-show', action='store_true',
                       help='Do not display plots (just save them)')

    args = parser.parse_args()

    # Use either positional argument or --file flag
    file_path = args.file_path or args.file_path_alt

    if not file_path:
        parser.error("Please provide a file path either as argument or using --file flag")

    return file_path, args.output, args.no_show


def main():
    """Main function to run the single file firmware log analysis."""
    try:
        # Parse command line arguments
        file_path, output_dir, no_show = parse_arguments()

        print("Single File Firmware Log Visualizer")
        print("=" * 50)
        print(f"Analyzing file: {file_path}")

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        print(f"Output directory: {output_path.absolute()}")

        # Initialize parser
        parser = SingleFileFirmwareLogParser()

        # Parse the specified file
        parser.parse_single_file(file_path)

        # Generate summary statistics
        print("\nGenerating summary statistics...")
        summary_df = parser.get_summary_statistics()
        print(f"Summary statistics generated for {len(summary_df)} parameters.")

        # Save summary to CSV
        csv_filename = output_path / 'summary.csv'
        summary_df.to_csv(csv_filename, index=False)
        print(f"Summary statistics saved to '{csv_filename}'")

        # Create visualizer
        visualizer = SingleFileFirmwareLogVisualizer(parser)

        # Create and save multi-subplot figure
        print("\nCreating visualization...")
        fig = visualizer.create_multi_subplot_figure()

        # Use input file's base name for PNG output filename
        input_file_stem = Path(file_path).stem  # Gets filename without extension
        plot_filename = output_path / f'{input_file_stem}.png'

        fig.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"Parameter visualization saved as '{plot_filename}'")

        # Display plot unless --no-show is specified
        if not no_show:
            plt.show()

        print("\nAnalysis completed successfully!")
        print(f"Analyzed file: {Path(file_path).name}")
        print(f"Found {len([p for p, v in parser.parsed_data.items() if v])} parameters with data")
        print(f"Total measurements: {sum(len(v) for v in parser.parsed_data.values())}")
        print(f"Output files saved to: {output_path.absolute()}")

    except Exception as e:
        print(f"Error during analysis: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
