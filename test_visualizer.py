#!/usr/bin/env python3
"""
Test script for the firmware log visualizer.

This script demonstrates how to use the FirmwareLogParser and FirmwareLogVisualizer
classes to analyze firmware log data.
"""

from firmware_log_visualizer import FirmwareLogParser, FirmwareLogVisualizer
import matplotlib.pyplot as plt

def test_basic_functionality():
    """Test basic functionality of the parser and visualizer."""
    print("Testing Firmware Log Visualizer...")
    print("=" * 50)
    
    # Initialize parser
    parser = FirmwareLogParser("tests/firmware")
    
    # Parse files
    parser.parse_all_files()
    
    # Print summary information
    print(f"\nParsed {len(parser.parsed_data)} files:")
    for filename, file_info in parser.parsed_data.items():
        filter_val = file_info['filter_value']
        total_params = sum(len(values) for values in file_info['data'].values() if values)
        print(f"  - {filename} (filter={filter_val}): {total_params} parameter values")
    
    # Generate summary statistics
    summary_df = parser.get_summary_statistics()
    print(f"\nSummary statistics:")
    print(f"  - Total parameter-file combinations: {len(summary_df)}")
    print(f"  - Unique parameters: {summary_df['parameter'].nunique()}")
    print(f"  - Files processed: {summary_df['filename'].nunique()}")
    
    # Show top parameters by data count
    print(f"\nTop 10 parameters by data count:")
    top_params = summary_df.groupby('parameter')['count'].sum().sort_values(ascending=False).head(10)
    for param, count in top_params.items():
        print(f"  - {param}: {count} values")
    
    # Create visualizer
    visualizer = FirmwareLogVisualizer(parser)
    
    # Test creating a focused visualization with just a few key parameters
    key_parameters = ['dBrightness', 'dContrast', 'dSaturation', 'dBacklight', 
                     'mid_boost', 'highlight_stretch', 'contrast_boost', 'intensity_indicator_PQ']
    
    print(f"\nCreating focused visualization with {len(key_parameters)} key parameters...")
    fig = visualizer.create_multi_subplot_figure(parameters_to_plot=key_parameters, figsize=(16, 12))
    fig.savefig('test_focused_visualization.png', dpi=300, bbox_inches='tight')
    print("Focused visualization saved as 'test_focused_visualization.png'")
    
    # Test parameter comparison plot
    print("Creating parameter comparison plot...")
    comparison_groups = {
        'Main Processing': ['dBrightness', 'dContrast', 'dSaturation', 'dBacklight'],
        'Level Controls': ['mid_boost', 'highlight_stretch', 'shadow_drop', 'contrast_boost'],
        'Quality Metrics': ['confidence', 'intensity_indicator_PQ', 'chroma_indicator'],
        'Gain Parameters': ['gainPos_dBrightness', 'gainPos_dContrast', 'gainPos_dSaturation']
    }
    
    fig2 = visualizer.create_parameter_comparison_plot(comparison_groups, figsize=(14, 10))
    fig2.savefig('test_parameter_comparison.png', dpi=300, bbox_inches='tight')
    print("Parameter comparison plot saved as 'test_parameter_comparison.png'")
    
    # Show some interesting statistics
    print(f"\nInteresting findings:")
    
    # Find parameters with varying values
    varying_params = summary_df[summary_df['std'] > 0]['parameter'].unique()
    if len(varying_params) > 0:
        print(f"  - Parameters with varying values: {len(varying_params)}")
        for param in varying_params[:5]:  # Show first 5
            param_data = summary_df[summary_df['parameter'] == param]
            print(f"    * {param}: std={param_data['std'].iloc[0]:.2f}")
    else:
        print("  - All parameters have constant values across measurements")
    
    # Find parameters with most data points
    high_count_params = summary_df.groupby('parameter')['count'].sum().sort_values(ascending=False).head(3)
    print(f"  - Parameters with most data points:")
    for param, count in high_count_params.items():
        print(f"    * {param}: {count} total measurements")
    
    print("\nTest completed successfully!")
    return True

def demonstrate_custom_analysis():
    """Demonstrate how to do custom analysis with the parsed data."""
    print("\nDemonstrating custom analysis...")
    print("-" * 30)
    
    parser = FirmwareLogParser("tests/firmware")
    parser.parse_all_files()
    
    # Example: Analyze how parameters change with filter values
    print("Filter value analysis:")
    for filename, file_info in parser.parsed_data.items():
        filter_val = file_info['filter_value']
        brightness_values = file_info['data']['dBrightness']
        contrast_values = file_info['data']['dContrast']
        
        if brightness_values and contrast_values:
            avg_brightness = sum(brightness_values) / len(brightness_values)
            avg_contrast = sum(contrast_values) / len(contrast_values)
            print(f"  Filter {filter_val}: Brightness={avg_brightness:.0f}, Contrast={avg_contrast:.0f}")
    
    # Example: Find correlations between parameters
    print("\nParameter relationships (within each file):")
    for filename, file_info in parser.parsed_data.items():
        filter_val = file_info['filter_value']
        data = file_info['data']
        
        # Check if brightness and contrast have the same number of measurements
        brightness = data['dBrightness']
        contrast = data['dContrast']
        
        if len(brightness) > 0 and len(contrast) > 0:
            if len(brightness) == len(contrast):
                # Simple correlation check (all values are the same in this case)
                brightness_unique = len(set(brightness))
                contrast_unique = len(set(contrast))
                print(f"  Filter {filter_val}: Brightness has {brightness_unique} unique values, "
                      f"Contrast has {contrast_unique} unique values")

if __name__ == "__main__":
    # Run tests
    success = test_basic_functionality()
    
    if success:
        demonstrate_custom_analysis()
        print("\nAll tests passed! The firmware log visualizer is working correctly.")
    else:
        print("Tests failed!")
