#!/usr/bin/env python3
"""
Quick Start Example for Firmware Log Analyzer

This script demonstrates the simplest way to use the firmware log analyzer
to parse and visualize your firmware log data.
"""

import sys
from pathlib import Path
from firmware_log_visualizer import SingleFileFirmwareLogParser, SingleFileFirmwareLogVisualizer
import matplotlib.pyplot as plt

def quick_analysis(file_path=None):
    """Perform a quick analysis of a single firmware log file."""
    print("🚀 Quick Single File Firmware Log Analysis")
    print("=" * 50)

    # Determine which file to analyze
    if not file_path:
        # Default to a test file
        test_files = [
            'tests/firmware/L17-IC-filter_0.5.txt',
            'tests/idk/TC_Flash_first_few_frames_pq_log.txt'
        ]

        for test_file in test_files:
            if Path(test_file).exists():
                file_path = test_file
                break

        if not file_path:
            print("❌ No test files found. Please provide a file path.")
            return False

    print(f"📁 Analyzing file: {file_path}")

    # Step 1: Parse the log file
    print("📊 Parsing firmware log file...")
    parser = SingleFileFirmwareLogParser()
    parser.parse_single_file(file_path)

    # Step 2: Show what we found
    params_with_data = sum(1 for values in parser.parsed_data.values() if values)
    total_measurements = sum(len(values) for values in parser.parsed_data.values())

    print(f"✅ Successfully parsed file!")
    print(f"📊 Found {params_with_data} parameters with data")
    print(f"📈 Total measurements: {total_measurements}")

    # Step 3: Generate summary
    summary = parser.get_summary_statistics()

    # Step 4: Create visualizations
    print("🎨 Creating visualizations...")
    visualizer = SingleFileFirmwareLogVisualizer(parser)

    # Create output directory
    output_dir = Path('quick_analysis_output')
    output_dir.mkdir(exist_ok=True)

    # Get input file stem for naming output files
    input_file_stem = Path(file_path).stem

    # Create the main analysis plot
    fig1 = visualizer.create_multi_subplot_figure(figsize=(20, 15))
    fig1.savefig(output_dir / f'{input_file_stem}_all_parameters.png', dpi=300, bbox_inches='tight')
    print(f"💾 Saved: {output_dir / f'{input_file_stem}_all_parameters.png'}")

    # Create a focused plot with just the most important parameters (if available)
    key_params = ['dBrightness', 'dContrast', 'dSaturation', 'dBacklight',
                  'mid_boost', 'highlight_stretch', 'intensity_indicator_PQ']
    available_key_params = [p for p in key_params if p in parser.parsed_data and parser.parsed_data[p]]

    if available_key_params:
        fig2 = visualizer.create_multi_subplot_figure(
            parameters_to_plot=available_key_params,
            figsize=(16, 10)
        )
        fig2.savefig(output_dir / f'{input_file_stem}_key_parameters.png', dpi=300, bbox_inches='tight')
        print(f"💾 Saved: {output_dir / f'{input_file_stem}_key_parameters.png'}")

    # Save summary statistics
    summary = parser.get_summary_statistics()
    summary.to_csv(output_dir / 'summary.csv', index=False)
    print(f"💾 Saved: {output_dir / 'summary.csv'}")

    # Step 5: Show some interesting insights
    print("\n🔍 Quick Insights:")

    # Show file metadata
    metadata = parser.file_info['metadata']
    if 'filter_value' in metadata and metadata['filter_value'] is not None:
        print(f"   📋 Filter value: {metadata['filter_value']}")

    # Show top parameters by data volume
    top_params = summary.nlargest(5, 'count')
    print(f"   📊 Top 5 parameters by data volume:")
    for _, row in top_params.iterrows():
        print(f"      • {row['parameter']}: {row['count']} measurements")

    # Show parameters with varying values (if any)
    varying = summary[summary['std'] > 0]
    if len(varying) > 0:
        print(f"   🔄 Parameters with varying values: {len(varying)}")
        print(f"      Most variable: {varying.nlargest(1, 'std').iloc[0]['parameter']}")
    else:
        print(f"   📌 All parameters have constant values")

    print(f"\n✨ Analysis complete! Check the files in '{output_dir}' directory.")

    return True

def show_data_sample(file_path=None):
    """Show a sample of the extracted data."""
    print("\n📋 Data Sample:")
    print("-" * 20)

    if not file_path:
        # Default to a test file
        test_files = [
            'tests/firmware/L17-IC-filter_0.5.txt',
            'tests/idk/TC_Flash_first_few_frames_pq_log.txt'
        ]

        for test_file in test_files:
            if Path(test_file).exists():
                file_path = test_file
                break

    if not file_path:
        print("❌ No test files found.")
        return

    parser = SingleFileFirmwareLogParser()
    parser.parse_single_file(file_path)

    print(f"File: {parser.file_info['file_path'].name}")
    metadata = parser.file_info['metadata']
    if 'filter_value' in metadata and metadata['filter_value'] is not None:
        print(f"Filter Value: {metadata['filter_value']}")

    print("Sample parameters:")

    count = 0
    for param_name, values in parser.parsed_data.items():
        if values and count < 5:  # Show first 5 parameters with data
            print(f"  • {param_name}: {len(values)} values (e.g., {values[0]})")
            if len(values) > 1 and values[0] != values[-1]:
                print(f"    Range: {min(values)} - {max(values)}")
            count += 1

if __name__ == "__main__":
    # Check if file path provided as command line argument
    file_path = sys.argv[1] if len(sys.argv) > 1 else None

    try:
        success = quick_analysis(file_path)
        if success:
            show_data_sample(file_path)
            print("\n🎉 Quick start completed successfully!")
            print("💡 Tip: Check the 'quick_analysis_output' directory for your files!")
            if not file_path:
                print("💡 You can also specify a file: python quick_start_example.py <path_to_log_file>")
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure the file path is correct and the file exists")
