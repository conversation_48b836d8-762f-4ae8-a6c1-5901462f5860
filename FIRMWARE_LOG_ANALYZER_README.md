# Firmware Log Analyzer

A comprehensive Python script for parsing and visualizing Dolby debug firmware log files. This tool extracts numerical parameters from firmware logs and generates multi-subplot visualizations for easy analysis and comparison.

## Features

- **Automatic File Discovery**: Parses all `.txt` files in the `tests/firmware` directory
- **Comprehensive Parameter Extraction**: Extracts 27+ different Dolby debug parameters including:
  - Main processing parameters (dBrightness, dContrast, dSaturation, etc.)
  - Gain parameters (gainPos_*, gainNeg_*)
  - Level parameters (mid_boost, highlight_stretch, shadow_drop, etc.)
  - Quality indicators (confidence, intensity_indicator_PQ, etc.)
- **Multi-Subplot Visualization**: Creates separate subplots for each parameter type
- **Parameter Comparison**: Groups related parameters for comparative analysis
- **Statistical Analysis**: Generates summary statistics and exports to CSV
- **Filter Value Analysis**: Extracts filter values from filenames for correlation analysis

## Installation

### Prerequisites

Make sure you have Python 3.7+ installed. Then install the required dependencies:

```bash
pip install pandas matplotlib seaborn numpy
```

### Files

- `firmware_log_visualizer.py` - Main script with parser and visualizer classes
- `test_visualizer.py` - Test script demonstrating usage
- `FIRMWARE_LOG_ANALYZER_README.md` - This documentation

## Usage

### Basic Usage

Run the main script to analyze all firmware log files:

```bash
python firmware_log_visualizer.py
```

This will:
1. Parse all `.txt` files in `tests/firmware/`
2. Extract numerical parameters from Dolby debug logs
3. Generate summary statistics (`firmware_log_summary.csv`)
4. Create two visualization files:
   - `firmware_parameters_analysis.png` - Multi-subplot analysis
   - `firmware_parameter_comparisons.png` - Parameter group comparisons

### Advanced Usage

You can also use the classes programmatically for custom analysis:

```python
from firmware_log_visualizer import FirmwareLogParser, FirmwareLogVisualizer

# Initialize parser
parser = FirmwareLogParser("tests/firmware")
parser.parse_all_files()

# Create visualizer
visualizer = FirmwareLogVisualizer(parser)

# Create custom visualization with specific parameters
key_params = ['dBrightness', 'dContrast', 'dSaturation', 'mid_boost']
fig = visualizer.create_multi_subplot_figure(parameters_to_plot=key_params)
fig.show()

# Access raw data for custom analysis
for filename, file_info in parser.parsed_data.items():
    filter_value = file_info['filter_value']
    brightness_data = file_info['data']['dBrightness']
    print(f"Filter {filter_value}: {len(brightness_data)} brightness measurements")
```

### Test the Installation

Run the test script to verify everything works:

```bash
python test_visualizer.py
```

## Data Structure

### Input Files

The script expects firmware log files with the following characteristics:
- Located in `tests/firmware/` directory
- Text files with `.txt` extension
- Contain Dolby debug log entries in the format:
  ```
  [ timestamp] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG]parameter_name(value)
  [ timestamp] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG]parameter_name: value
  ```

### Extracted Parameters

The script extracts the following parameter categories:

#### Main Processing Parameters
- `dBrightness` - Display brightness adjustment
- `dContrast` - Display contrast adjustment  
- `dSaturation` - Color saturation adjustment
- `dBacklight` - Backlight control
- `dLocalContrast` - Local contrast enhancement
- `dBrightness_PR_on` - Precision rendering brightness

#### Gain Parameters
- `gainPos_precisionRendering` - Positive gain for precision rendering
- `gainPos_dLocalContrast` - Positive gain for local contrast
- `gainPos_dBrightness` - Positive gain for brightness
- `gainPos_dSaturation` - Positive gain for saturation
- `gainPos_dContrast` - Positive gain for contrast
- `gainNeg_dSaturation` - Negative gain for saturation
- `gainNeg_dContrast` - Negative gain for contrast

#### Level Parameters
- `mid_boost` - Mid-tone boost level
- `highlight_stretch` - Highlight stretching
- `shadow_drop` - Shadow dropping
- `contrast_boost` - Contrast boost level
- `saturation_boost` - Saturation boost level
- `detail_boost` - Detail enhancement boost
- `chroma_indicator` - Chroma processing indicator
- `intensity_indicator_PQ` - PQ intensity indicator
- `chroma_lift` - Chroma lifting adjustment

#### Quality Indicators
- `confidence` - Processing confidence level
- `precisionRenderingStrength` - Precision rendering strength
- `dSaturationPlusOne` - Saturation adjustment plus one
- `dContrastPlusOne` - Contrast adjustment plus one
- `UpMappingStrength` - Up-mapping strength

### Output Files

#### CSV Summary (`firmware_log_summary.csv`)
Contains statistical summary for each parameter in each file:
- `filename` - Source file name
- `filter_value` - Filter value extracted from filename
- `parameter` - Parameter name
- `count` - Number of measurements
- `mean` - Average value
- `std` - Standard deviation
- `min` - Minimum value
- `max` - Maximum value
- `median` - Median value

#### Visualization Files
- `firmware_parameters_analysis.png` - Multi-subplot figure with individual parameter plots
- `firmware_parameter_comparisons.png` - Grouped parameter comparison plots

## Customization

### Adding New Parameters

To extract additional parameters, add them to the `patterns` dictionary in the `FirmwareLogParser` class:

```python
self.patterns = {
    # Existing patterns...
    'new_parameter': r'new_parameter_pattern\((\d+)\)',
}
```

### Custom Parameter Groups

Modify the parameter groups in `create_parameter_comparison_plot()`:

```python
parameter_groups = {
    'Custom Group': ['param1', 'param2', 'param3'],
    # Other groups...
}
```

### Visualization Styling

The script uses seaborn styling. You can modify the style by changing:

```python
plt.style.use('your_preferred_style')
sns.set_palette("your_preferred_palette")
```

## Troubleshooting

### Common Issues

1. **No files found**: Ensure `.txt` files exist in `tests/firmware/` directory
2. **Import errors**: Install required dependencies with `pip install pandas matplotlib seaborn numpy`
3. **Empty plots**: Check that log files contain the expected Dolby debug format
4. **Memory issues**: For very large files, consider processing files individually

### Debug Mode

Add debug prints to see what's being extracted:

```python
# In parse_file method, add:
print(f"Found {len(matches)} matches for {param_name}")
```

## Example Output

The script successfully processes the provided firmware log files and generates:

- **81 parameter-file combinations** from 3 input files
- **Multi-subplot visualizations** showing parameter distributions
- **Statistical summaries** for all extracted parameters
- **Filter value correlations** showing how parameters vary with different filter settings

## License

This script is provided as-is for firmware log analysis purposes.
