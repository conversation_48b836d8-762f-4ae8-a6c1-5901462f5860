#!/usr/bin/env python3
"""
Dependency Test Script for Firmware Log Analysis Project

This script tests that all required dependencies are properly installed
and can be imported successfully.
"""

import sys
import importlib

def test_dependency(module_name, package_name=None):
    """Test if a dependency can be imported successfully."""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✅ {module_name}: {version}")
        return True
    except ImportError as e:
        package = package_name or module_name
        print(f"❌ {module_name}: MISSING - Install with 'pip install {package}'")
        print(f"   Error: {e}")
        return False

def main():
    """Test all required dependencies."""
    print("Firmware Log Analysis - Dependency Test")
    print("=" * 50)
    
    # Test core dependencies
    dependencies = [
        ('numpy', 'numpy>=1.24.0'),
        ('pandas', 'pandas>=2.0.0'),
        ('matplotlib', 'matplotlib>=3.7.0'),
        ('seaborn', 'seaborn>=0.12.0'),
    ]
    
    # Test standard library modules (should always work)
    stdlib_modules = [
        'os', 're', 'sys', 'math', 'argparse', 'pathlib', 'typing'
    ]
    
    print("\nTesting Core Dependencies:")
    print("-" * 30)
    all_good = True
    for module, package in dependencies:
        if not test_dependency(module, package):
            all_good = False
    
    print("\nTesting Standard Library Modules:")
    print("-" * 35)
    for module in stdlib_modules:
        if not test_dependency(module):
            all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 ALL DEPENDENCIES INSTALLED SUCCESSFULLY!")
        print("The firmware log analysis scripts should run without issues.")
        return 0
    else:
        print("❌ SOME DEPENDENCIES ARE MISSING!")
        print("Please install missing packages using:")
        print("pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    sys.exit(main())
